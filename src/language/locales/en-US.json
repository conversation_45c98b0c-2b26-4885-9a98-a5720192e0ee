{"1000": "America", "9998": "USD", "9999": "English", "USDT_Input_Addr_Err_Prompt": "Please enter the correct address", "USDT_address": "Address", "USDT_Blockchain": "Chain", "USDT_Dig_curr_channel_name": "Crypto", "USDT_addr_iput_desc_text": "Please paste your USDT address", "USDT_no_Addr_sub_err": "Please paste your USDT address", "USDT_Plac_for_Share_Link_Telegram": "🎉 I’m on BetFugu! Awesome slots, poker, fiat, and crypto. Join me and let’s play together! 🏆", "Reg_button": "Sign up", "Reg_Mob_phone_No": "Phone", "Reg_Verif_code": "OTP", "Login_button": "Sign in", "Login_Pls_check_box_agree_user_agrmt_policy": "I agree to the Terms of Service & Privacy Policy", "Home_page": "Home", "Recommend": "For You", "Home_Reels": "<PERSON><PERSON>", "earn_money": "Earn cash", "Home_Wallets": "Wallets", "Home_Messages": "Notifications", "Me_info": "Me", "Popular": "Hot", "Slot_machine": "Slots", "Casino_game": "Casino", "Poker_game": "Poker", "History": "History", "Collection": "Like", "Recharge": "<PERSON><PERSON><PERSON><PERSON>", "Withdrawal": "Withdraw", "Home_events": "Events", "Customer_service": "Customer Service", "wallet_deposit": "<PERSON><PERSON><PERSON><PERSON>", "wallet_Withdrawal": "<PERSON><PERSON><PERSON>", "Deposit_Amount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "Enter_withdraw_num_ 100_5W": "Enter withdraw amount: 100 - 50,000", "deposit_text_in5mins": "The recharge will be credited within 5 minutes.\nIf it fails to be credited, please provide your recharge Receipt to customer service", "Deposit_pro_text_USD": "1. No additional fees for deposits (except transaction fees)\n </br>\n 2. Please do not save the address for a second deposit\n </br>\n 3. Please do not deposit after the address expires", "Deposit_pro_text_PHP": "<span class=\"text-red\">₱{fund_deposite_nums}</span> deposit adds <span class=\"text-red\">1 </span> claim times of Relief funds.\n <br>\n The recharge will be credited within 5 minutes.\n If it fails to be credited, please provide your recharge Receipt to customer service\n <br>", "Wallet_deposit_Notice_USD": "<span class=\"text-red\">No betting amount requirement</span> <br>\n1. No additional fees for deposits (except transaction fees)\n </br>\n 2. Please do not save the address for a second deposit\n </br>\n 3. Please do not deposit after the address expires", "Wallet_deposit_Notice_PHP": "<span class=\"text-red\">No betting amount requirement</span> <br>\n<span class=\"text-red\">₱{fund_deposite_nums}</span> deposit adds <span class=\"text-red\">1 </span> claim times of Relief funds.\n <br>\n The recharge will be credited within 5 minutes.\n If it fails to be credited, please provide your recharge Receipt to customer service\n <br>", "Wallet_deposit_Notice_BRL": "<span class=\"text-red\">No betting amount requirement</span> <br>\n<span class=\"text-red\">R${fund_deposite_nums}</span> deposit adds <span class=\"text-red\">1 </span> claim times of Relief funds.\n <br>\n The recharge will be credited within 5 minutes.\n If it fails to be credited, please provide your recharge Receipt to customer service\n <br>", "Wallet_deposit_Notice_NGN": "<span class=\"text-red\">No betting amount requirement</span> <br>\n<span class=\"text-red\">₦{fund_deposite_nums}</span> deposit adds <span class=\"text-red\">1 </span> claim times of Relief funds.\n <br>\n The recharge will be credited within 5 minutes.\n If it fails to be credited, please provide your recharge Receipt to customer service\n <br>", "Wallet_deposit_Notice_TRY": "<span class=\"text-red\">No betting amount requirement</span> <br>\n<span class=\"text-red\">₺{fund_deposite_nums}</span> deposit adds <span class=\"text-red\">1 </span> claim times of Relief funds.\n <br>\n The recharge will be credited within 5 minutes.\n If it fails to be credited, please provide your recharge Receipt to customer service\n <br>", "Deposit_req_bind_phone": "Recharge requires users to complete mobile phone binding", "Submit": "Submit", "Bind_bank_account": "Bind your bank account", "Available_withdrawal": "<PERSON><PERSON><PERSON><PERSON>", "Select_withdraw_amount": "Select the withdrawal amount", "If_withdrawal_amount": "If withdrawal > 300,</br>\nWithdrawal fee = {currency} 20\n</br>\nIf withdrawal ≤ 300,</br>\nWithdrawal fee = Withdrawal amount*2%+ {currency} 20", "Wallet_withdr_info_USD": "1. No additional fees for withdrawals (except transaction fees)\n</br>\n2. Please confirm your payment address information", "Wallet_withdr_info_PHP": "95%  of withdrawals will arrive within 2 minute.（except channel reasons）\nWithdrawal fee = ₱20", "Wallet_withdr_info_NGN": "95%  of withdrawals will arrive within 2 minute.（except channel reasons）\nWithdrawal fee = Withdrawal amount*1%", "Wallet_withdr_info_BRL": "95%  of withdrawals will arrive within 2 minute.（except channel reasons）", "Wallet_withdr_info_TRY": "95%  of withdrawals will arrive within 2 minute.（except channel reasons）\nWithdrawal fee = Withdrawal amount*5%", "Withdrawal_record": "Withdraw Records", "Record": "Records", "Security_Center": "Security", "Game_History": "History", "Message_Notification": "Notifications", "Account_Balance": "Balance", "Language_Selection": "Language", "Settings": "Setup", "Customer_Service": "Customer Service", "Redemption_Code": "Redemp Code", "Logout": "Log out", "Time": "Time", "Amount": "Amount", "Status": "Status", "Withdrawal_Failed": "Failed", "Withdrawal_Completed": "Finished", "Rejected": "Declined", "Channel": "Channels", "Balance": "Balance", "Account_Name": "Account", "First_Name": "First Name", "Surname": "Last Name", "Please_enter_account": "Please enter your account name", "Please_enter_name": "Please enter your first name", "Please_enter_lastname": "Please enter your last name", "Bind_Payment_Account": "Bind account", "Login_Expired_login_again": "The login expired, please log in again", "No_more_content": "No More", "Swipe_up_show_more": "Swipe up", "Login_info_unavailable_again": "The login information is invalid, please log in again", "Wrong_mobile_number": "The mobile number is incorrect, please re-enter", "Wrong_verification": "The verification code is wrong, please re-enter", "Login_failed_please": "The login failed, please try again", "User_not_exist": "The user does not exist", "NO_Bind_time_limit": "Binding timed out. Please try again.", "User_has_been_bound": "The user has been bound", "Binding_prohibite": "No binding allowed", "Binding_successful": "Binding successful", "Mobile_number_wrong": "The mobile number is incorrect, please re-enter", "SMS_verification_wrong": "The SMS verification code is incorrect, please re-enter", "Mobile_number_bound": "The mobile number has been bound", "Nobound_phone_cant_change": "The mobile phone number is not bound and cannot be changed", "You_no_permission_upload": "You do not have permission to upload", "Upload_timed_out_tryagain": "Upload timed out, please try again", "Upload_failed_tryagain": "The upload failed, please try again", "Send_failed_wait": "Send failed, please try again later", "Too_many_times_sent_wait": "There are too many attempts to send, please try again later", "SMS_num_max_tomor_try": "The SMS limit has been exceeded today. Please try again tomorrow.", "Configuration_error": "Configuration error", "You_do_not_have_perm": "You do not have permission to modify", "Language_selection_error": "Wrong language selection", "Currency_selection_error": "Wrong currency selection", "Deposit_channel_error_tryagain": "Deposit channel error, please try again", "Deposit_amount_error_tryagain": "Wrong deposit amount, please try again", "Deposit_limited_call_cs": "Deposit is limited, please get in touch with customer service", "Failed_create_order": "Failed to create an order", "[1090]currency_no_found": "No corresponding currency was found", "Currency_no_found": "No corresponding currency was found", "Phone_bing_maximum_2": "You can bind up to 2 mobile phone numbers", "Withdrawals_are_proh": "Withdrawals are prohibited", "Withdrawal_balance_i": "Insufficient withdrawal balance", "Withdrawal_times_are": "Insufficient withdrawal times", "Withdrawal_failed": "Failed", "Set_language_currency": "Set language and currency", "Set_main_account_relog": "Please set the primary account and log in again", "Sub_account_creat_error_tryagin": "Subaccount creation error, please try again", "Failed_enter_tryagain": "Failed to enter the game, please try again", "Failed_convert_currency_tryagain": "Failed to convert currency, please try again", "Failed_re_enter_tryagain": "Failed to re-enter the game, please try again later", "Ingame_re_enter": "Currently playing the game. Do you want to re-enter the game?", "Set_language_success": "Set language successfully", "Set_language_fail": "Failed to set language", "Enter_cor_phone_code": "Please enter the correct mobile phone number and verification code", "No_withdrawal_record": "No withdrawal records", "No_game_record": "No records, hurry up and invite friends to play together~", "No_recharge_record": "No recharge records", "Recharge_successful": "recharge Successful", "Application_submit": "Successful Submission", "Withdrawal_application_wait_CS": "Withdrawal application has been submitted.\nThe application is expected to be processed within 3-5 hours, and may be extended on non-working days or during working hours.", "${newloin}": "Welcome to BetFugu, an absolutely fair game platform offering an unrivalled gaming experience with colorful choices, top security, and generous rewards.\nJoin now to dive into top-notch gaming fun!", "${bindshare}": "Congratulations, {uid} was bound to your account. More income is on the way~Wish you earn more!", "${receiver.gold}": "System transferred {count} {name} to you", "${payment}": "{count} {name} has been recharged successfully", "${receiver.signActivitiy}": "Congratulations on completing  {count} {currency}. I hope you win money every day!", "${receiver.bindtelephone}": "Phone number successfully bind. {count} chips have been sent to your account.", "${receiver.paymentrebate}": "Congratulations on completing \"Recharge Rebate\" {count} {currency} I wish you win money every day!", "${uesr.bindshare}": "Hi, there 😊\nThank you for taking my invitation, and thank you for your trust! BetFugu is fantastic.You can play various games and cash out your game money.You can also promote this app to earn money, Check the tutorial through the \"Earn Cash“ entry on the home page. If you have any questions, you can come to me. Let‘s win and earn together. Good luck😊!", "Confirm": "Confirm", "Cancel": "Cancel", "Bind": "Bind", "Already_bound": "Bound", "Nickname": "nickname", "Bind_mobile_phone": "Mobile Phone", "Bind_inviter": "Bind Inviter", "Personal_page": "Profile", "Are_you_sure_exit": "Are you sure to log out?", "Are_you_sure_modify_nickname": "Are you sure you want to change your nickname?", "Modification_failed": "Change failed, please try again later", "Modification_avater_fail": "Change failed, please try again later", "Copy_successfully": "<PERSON>pied", "Bonus_question_mark": "Bonus Explanation\n</br>\n1% of your daily total bets from your bonus will be automatically converted into chips the next day.\n</br>\nExceptions：</br>\n1.Tongits: Calculated by base points per game × 0.4.\n</br>\n 2.<PERSON><PERSON><PERSON>: Calculated according to the game's total win and lose amount × 1%.", "Please_enter_the_redemption": "Please enter your redemption code", "Redemption_code_no_null": "The redemption code can not be empty", "Redemption_code_uncor": "Incorrect gift code format", "Gift_redemption_succ": "Redemption Successful", "Legal_currency": "Fiat Currency", "Virtual_currency": "Virtual Currency", "Stay_tuned": "Coming Soon", "Ok": "Ok", "Choose_currency": "<PERSON><PERSON>", "Game": "Games", "Bet": "Settle", "Loading_please_wait": "Loading... please wait", "No_more": "No more", "Total": "Total", "Type": "Categories", "Confirm_and_submit": "Confirm", "Deposit_check_cs": "The recharge will be credited within 5 minutes.\nIf it fails to be credited, please provide your recharge Receipt to customer service", "If_withdrawal_1": "If withdrawal>300, Withdrawal fee=PHP 20", "If_withdrawal_2": "If withdrawal≤300, Withdrawal fee=withdrawal amount*2%+PHP 20", "Withdrawal_balance_info": "<span class=\"\"color-tip\"\">Ways to increase the balance that can be withdrawn:  </span>\n-20% of all games bet, except JILI games are credited to the Withdrawable balance    \n-10% of JILI game bets are credited to the Withdrawable balance\n</br>\n<span class=\"\"color-tip\"\">How to calculate betting amount:</span>\n-Tasty, color game, lucky joker, fruit fever, bring horse racing and mines are calculated    \naccording to the actual bet amount  \n-Super scratch is calculated according to the amount spent on tickets that have been scratched    \n-Tongits base points per game*35    \n-<PERSON><PERSON><PERSON>, Poker are calculated according to the game's total win and lose amount*0.7\n</br>\n<span class=\"\"color-tip\"\">For example:</span>\n-Lucky joker bet amount 20, the cashable    \nbalance increases by 4  \n-Tasty bet amount 10, cashable balance   \nincreases by 2  \n-fruit fever bet amount 30, cashable balance  \nincreases by6   \n-Color game bet amount 40, cashable \nbalance increases by 8   \n-Bingo bet amount 10, the cashable balance  \nincreases by 2   \n-Supers cratch: scratch a lottery ticket    \nbought 10 chips and increased your   \ncashable balance by 2    \n-Tongits 5 base points for 1 pot, the   \nwithdrawal balance will increase by 35   \n-<PERSON><PERSON><PERSON> lost 100 in the 1st game, won 100 in the 2nd game, and the withdrawable balance   \nincreases by 28 \n-<PERSON> wins 100 in the game, the withdrawable    \nbalance increases by 14  \n-Horse racing bet amount 10, the cashable   \nbalance increases by 2   \n-Mines bet amount 10, cashable balance   \nincreases by 2  \n-Crash bet amount 10, cashable balance  \nincreases by 2   \n<span class=\"\"small-tip\"\">No recharge, maximum amount to withdraw is 300PHP\n    All remaining chips will be removed.</span>", "Withdrawal_balance_info_PHP": "<strong>How to Increase Your Withdrawable Balance:</strong><br><li>Net winnings will be added to your withdrawal balance.</li><br>", "Withdrawal_balance_info_USD": "<strong>How to Increase Your Withdrawable Balance:</strong><br><li>Net winnings will be added to your withdrawal balance.</li><br>", "Withdrawal_balance_info_BRL": "<strong>How to Increase Your Withdrawable Balance:</strong><br><li>Net winnings will be added to your withdrawal balance.</li><br>", "Withdrawal_balance_info_TRY": "<strong>How to Increase Your Withdrawable Balance:</strong><br><li>Net winnings will be added to your withdrawal balance.</li><br>", "Withdrawal_balance_info_NGN": "<strong>How to Increase Your Withdrawable Balance:</strong><br><li>Net winnings will be added to your withdrawal balance.</li><br>", "Browser_no_supply_reels": "Your browser does not support the video", "{nickname}_enter_game": "{nickname} enter the Game", "Play": "Play", "facebook": "Facebook", "Copy_link": "Link", "Share_to_friends_now": "Share with friends now", "Link_copied": "<PERSON>pied", "Successfully_added": "Favorites Success", "Event": "Events", "System_message": "Notifications", "Transaction_message": "Transaction", "Promotion_ranking": "Earning Ranking", "Learn_more": "Learn More", "See_more": "View More", "How_to_make_money": "How to earn money?", "Invite_to_earn": "Invite to Earn", "earnMoney_rule_page": "Invite your friends to register by your invitation link, and you'll start earning money as long as they play games!\n</br>\n</br>\nEarn <span class=\"d1\">{num}</span> of revenue contributed by <span class=\"d2\">T1</span>  players plus <span class=\"d1\">{num1}</span> \nof revenue contributed by <span class=\"d2\">T2</span>  players.", "earnMoney_rule_title": "How to earn money", "earnMoney_rule_page1": "Invite valid players to register and play games, you'll earn money from the revenue they contribute!", "earnMoney_rule_page2": "What is a valid player?\n</br>\n- Register through your invitation link;\n</br>\n- Play at least one game.", "earnMoney_rule_page3": "How much money can I earn?\n</br>\n- The amount of money you can earn depends on the revenue contributed by T1 and T2 players of you.\n</br>\n- T1 players are players who registered through your invite link, T2 players are players who registered through the invite link of T1 players.\n</br>\n- You can earn <span class=\"d1\">{num}</span> of the revenue contributed by <span class=\"d2\">T1</span> players and <span class=\"d1\">{num1}</span> of the revenue contributed by <span class=\"d2\">T2</span> players.", "earnMoney_rule_page4": "Want to earn money easier and faster?\n</br>\n- You can join the group to get the secrets to getting rich! ", "earnMoney_rule_page5": "Those with 3 stars and above can join the Elite Club. Those with 4 stars and 5 stars will receive additional monthly rewards after joining the Elite Club.\n</br>\n</br>\nIf the required star rating is not reached this month, the next month's star rating will be vacated based on actual achievement.", "Join_the_group": "Join the group", "Elite_plan": "Elite Program", "Alamin_ang_elite_clu": "Alamin ang Elite Club", "Join_our_VIP_elite": "Join our VIP Elite Club", "earnMoney_pop_up_text1": "Due to your excellent performance, we would like to inform you of a special invitation to join our VIP Elite Club!", "earnMoney_pop_up_text2": "Right now, we are full of more than 871 awesome members who are earning awesome monthly income.", "earnMoney_pop_up_text3": "Click the button now to find out more information about Elite Club, join now so we can earn more money together!", "please_input_integer": "Please input integer", "Enter_redeem_amount": "Enter the amount you want to redeem", "Redeem": "Redeem", "PROMOTER_RANK": "High-Earning Rank", "T1_Players": "T1 Players", "Top_100_Contribution": "Top 100 Contributions", "Earned_Details": "Earned <PERSON>", "My_Invites": "My Invites", "Total_Earned": "Total", "Month_Earned": "Monthly", "earnMoney_carousel_info": "{nickname} invitation rewards have exceeded ₱{num}!!!", "Redemption_successful": "Redemption successful", "Pull_down_to_refresh": "Pull down", "Feedback_successful": "Feedback successful", "Abnormal_account_check": "Account login anomaly detected, please check your account security immediately. ", "Any_question_CS": " If you have any questions, please contact customer service", "recharge_rebate_info": "Within 7 days after completing the first recharge, you can\nreceive the corresponding rewards if the accumulated amount\nreaches the following amount.", "Loss_wallet_compensa_1": "We pay out if you lose, <span>up to 100P</span>\n</br>\nParticipation conditions: <span>within 7 days after completing the\nfirst deposit.</span>", "Loss_wallet_compensa_2": "Maximum 2000P, the excess amount will not be calculated.\nOnly users who have completed the first recharge can\nparticipate in this event. The event lasts for 7 days. After the\nevent ends, all rewards will be automatically distributed to\nyour account.", "Loss_wallet_compensa_3": "Your loss", "Loss_wallet_compensa_4": "Compensation", "Loss_wallet_compensa_5": "<500", "Loss_wallet_compensa_6": "{count} {curency}", "Bets_delay_5min": "There will be a certain time delay in recording game bets,\nwhich will not exceed 5 minutes.", "Sign_in_banner_copy": "Sign in and get cash", "Chat_interface_title": "Messages", "Report": "report", "Login": "Sign in", "Waiting_to_receive": "Unclaimed", "Received": "Claimed", "Pop_up_prompt_text": "The game server is under maintenance", "Not_logged_in_text": "Log in to get free cash bonus", "Not_logged_in_prompt": "Sign Up", "The_system_detects_country": "The system detects that your country/region is", "Language_is": "Language is", "Currency_type_is": "Currency is", "Confirm_language": "Confirm language", "No_need_to_change": "No change", "I_want_to_change": "Change", "Confirm_change_or_not": "Please confirm whether to change", "Login_bottom_agreeme": "I agree to BetFugu <span class=\"state-notice service\">Terms and Service</span> and\n            <span class=\"state-notice policy\">Privacy Policy</span>", "Or_sign_in_with": "Or sign in with", "Visitor": "Guest", "Send_verification_code": "Send code", "Resend": "Resend", "Enter_mobile_number": "Enter your phone number", "Enter_verification_code": "Enter your code", "Language_settings": "Language settings", "Sign_in": "Sign in to get rewards", "Recharge_rebate": "Recharge Rebate", "Never_lose_money": "Never lose money", "Ays_want_logout": "Confirm to exit？", "Long_lasting_effect": "Long-lasting effectiveness", "Unavailable": "Not currently available", "Copy_invitation_code": "Copy code", "Win": "win", "Lose": "lose", "Data_refresh_wait": "The data is expected to be refreshed. If you cannot click the confirmation button below, please wait for 3-5 seconds", "Failed_bind_inviter_ID": "Failed to bind the inviter", "Dyw_bind_inviter_ID": "Do you bind this inviter ID", "Enter_the_correct_ID": "Please enter the correct inviter ID", "ID_no_exist": "ID doesn't exist", "Enter_your_inviter_ID": "Enter your inviter ID", "Cannot_bind_yourself": "Cannot bind yourself", "Relief_fund_collection_info": "You can also receive {count} times, the deposit can increase the number of times", "Successful_collection": "Received successfully", "${receiver.reliefaward}": "Dear user, the relief fund [{count} {currency}] has been sent to your account balance.  I hope you win money!", "Successfully_bound": "Successfully bound", "Claim": "<PERSON><PERSON><PERSON>", "Refresh_after_release": "Release to refresh", "Login_failed_tryagain": "The login failed, please log in again later", "Incorrect_verification": "Incorrect verification code, please re-enter.", "Please_enter_a_valid": "Please enter a valid phone number format", "Verification_code_ha": "The verification code has been sent, please check", "next_step": "next", "Provide_more_detail": "Please provide specific details so we can resolve your issue as quickly as possible.", "Reason": "Reason", "Optional": "Optional", "Describtion": "Description", "Politics_Related": "Politics Related", "Sexually_Explicit": "Sexually Explicit", "Scam": "Scam", "Insult": "Insult", "Other": "Others", "Redemption": "Redemp Code", "Redemption_Record": "Redemption Records", "Withdraw_must_bind_phone": "You can withdraw cash after binding your mobile phone number", "Incorrect_amount_enter_right": "Incorrect amount. Please enter the right amount!", "cash_out_invite_5people": "You can cash out only after inviting 5 people to play games", "wallet_withd_fee_info": " If you have any problems during the withdrawal process, please contact online customer service. ", "wallet_withd_fee_info_PHP": "Withdrawal fee will be ₱{count} ,and the actual amount to receive will be ₱{count1} .", "wallet_withd_fee_info_USD": "Withdrawal fee will be ${count} ,and the actual amount to receive will be ${count1} .", "wallet_withd_fee_info_INR": "Withdrawal fee will be ₹{count} ,and the actual amount to receive will be ₹{count1} .", "wallet_withd_fee_info_IDR": "Withdrawal fee will be Rp{count} ,and the actual amount to receive will be Rp{count1} .", "wallet_withd_fee_info_BRL": "Withdrawal fee will be R${count} ,and the actual amount to receive will be R${count1} .", "wallet_withd_fee_info_NGN": "Withdrawal fee will be ₦{count} ,and the actual amount to receive will be ₦{count1} .", "wallet_withd_fee_info_TRY": "Withdrawal fee will be ₺{count} ,and the actual amount to receive will be ₺{count1} .", "Network_error_tryagain": "Network error, please try again later", "${payout.ok}": "Congratulations! Your application was approved. Chips {count} have been issued to your gcash account. ", "Unreviewed": "Unreviewed", "Reviewed_order_is_withdrawal": "Withdrawing", "Withdrawal_successful": "Successful", "Review_failed": "Failed the review", "To_be_determined": "To be determined", "Withdrawal_times_max_info": "{count} times of cash out remaining for today", "Confirm_cancellation": "Do you want to cancel this withdrawal?", "Convert_chips_failed": "Chip conversion failed, please try again", "System_maintenance": "System maintenance", "Connection_in_use": "Please log in again", "Account_banned_contation_cs": "This account has been banned, please contact the administrator", "Token_expired_log_in": "This login information has expired, please log in again", "Redemption_code_no_exist": "redemption code does not exist", "Not_paying_user_not_exchange": "The redemption code cannot be used", "Wrong_channel_not_exchange": "The channel is incorrect and the redemption code cannot be used", "Wrong_upstream_not_exchange": "The bound inviter is wrong and the redemption code cannot be used", "Not_within_event_time": "Not during event time", "Redemption_code_unavailable": "The redemption code is invalid and cannot be redeemed", "Exceeded_the_daily": "Exceed the daily limit and cannot be redeemed", "User_has_received": "You have already received the rewards and cannot receive it again", "Receiving": "Receiving", "Exceeded_the_maximum": "Exceed the maximum number of redemptions and cannot be redeemed", "Verification_failed": "Authentication failed", "User_error": "User information error", "User_has_bound": "The user has been bound", "Account_has_bound": "Account already bound", "Email_format_wrong": "Incorrect email format", "Email_has_used": "Email already in use", "Verification_code_err": "Verification code error", "Send_failed_try_again": "Send failed, try again later", "Validity_period_in_5mins": "Valid for 5 minutes", "Currency_needs_dispose": "You need to set the currency type", "${receiver.lostmoneyrebate}": "You participated in the event \"LostMoneyRebate\" and received the reward {count}", "Account_binding_fail": "Account binding failed", "Not_Set": "Not Set", "link": "link", "Link_with_Facebook": "Link with Facebook", "Link_with_Google": "Link with Google", "Terms_and_Service": "Terms and Service", "Privacy_Policy": "Privacy Policy", "send": "send", "New": "New", "Write_someting": "Write something", "Order_is_being_gener": "Order is being generated", "Total_Gain": "Total Gain", "Rewards": "Rewards", "Amount_due": "Amount due", "relief_unbind_phone": "You can only receive relief funds after binding your mobile phone", "relief_notimes_1": "You can currently receive {count} times of relief funds. You can get more times by recharging", "relief_notimes_2": "The number of redemption times is exhausted, recharge to get more times", "Slide_it": "Slide it！", "rebate_requirement_more5": "Rewards can only be redeemed when the number of invitees is greater than 5", "Click_multiple_times": "Transaction in progress, please wait patiently", "Reminder_bindphone_reward": "The mobile phone binding reward {count}{currency} has been issued to your account", "Lifetime_Rank": "Lifetime Rank", "Monthly_Rank": "Monthly Rank", "Seed_user_pop_up_tit": "Become A Director", "Seed_user_pop_up_tex": "Join us and <PERSON><PERSON> A Director \n</br>\nwho can receive Lifetime Rewards in BetFugu!", "Seed_user_pop_up_button": "Join To <PERSON>arn <PERSON>", "${receiver.directorActivitiy@register}": "This is your Rewards for becoming our Director: {gold}{currency}", "${receiver.directorActivitiy@montday}": "This is your Director's Dividend for yesterday: {gold}{currency}", "Community": "Community", "Media": "Media", "Download_prompt_text": "Download for  {text}  sign-on bonus", "Download_prompt_text1": "Download for Fast & Secure Gaming", "USD_d_text_num": "$5", "PHP_d_text_num": " ₱420", "TRY_d_text_num": "₺180", "BRL_d_text_num": "R$30", "IDR_d_text_num": "Rp80000", "NGN_d_text_num": "₦9000", "INR_d_text_num": "₹500", "Christmas_start_info": "1. Attention, BetFugu Christmas campaign!", "Christmas_cashback_1": "Christmas Cashback is here to offer you a second chance at victory! Get ready to claim your daily cashback and relive the thrill of your gaming adventures once more.", "Understanding_Cashback": "2. Understanding Cashback:", "Christmas_cashback_2": "For those who are new to the concept, cashback is a delightful way for players to recoup a portion of their losses from previous gaming sessions. It's like having a safety net – even when luck isn't on your side, you'll still walk away with a little something to soften the blow. ", "How_it_works": "3. How it works:", "Christmas_cashback_3": "Participate in games (except tongits, pusoy) during the campaign period and get a cashback of 50% of your net loss the next day! It's a rewarding journey where your dedication and commitment to gaming excellence payoff in dividends. ", "Time_is_of_Essence": "4. Time is of Essence: ", "Christmas_cashback_4": "It's important to note that your cashback rewards are only available for the whole campaign. So, be sure to claim them promptly to make the most of your gaming experience and maximize your rewards!", "Christmas_cashback_5": "Don't let yesterday's missed opportunities hold you back. Claim your cashback and get ready for another day of thrilling adventures at BetFugu! Your rewards await – are you ready to seize them?", "Total_Cashback": "Total Cashback", "Claimable_time": "Claimable time", "Daily_Cashback": "Daily Cashback", "My_Rewards": "My Rewards", "Cashback_reward_info": "- Bet today and enjoy cashback rewards tomorrow!", "Total_amount_returne": "Total amount returned", "Event_Time": "Event Time", "Daily": "Daily", "Cashback_Magic": "Cashback Magic", "Christmas_campaign": "Event Introduction", "Cashback_Details": "Cashback Details ", "${receiver.rebate}": "Congratulations on getting {} ₱ through the Christmas event", "Tips_for_binding_gcash": "You can participate in this activity only after binding Gcash", "Total_amount_expecte_return": " Total amount expected to be returned", "Tip_text_receipt_record_info": "No record, hurry up and participate in the event to win cashback", "Cumulative_users": "Total users", "Cumulative_income": "Cumulative income", "Today": "Today", "Increase": "New user", "Active": "Active", "Income": "Income", "Season": "This issue", "Agent": "Agent", "Master": "Master", "Download": "Download", "First_pop_up_language": "Choose Your Language", "First_pop_up_currency": "Choose Your Currency", "First_pop_up_currency_info": "Games will use this currency for transactions.", "Refreshed": "Refreshed", "ios_add_to_desktop": "lOS instruction\n", "Open_in_Safari": "Open in Safari", "open_page_safari": "If you are in any other browser, open this page with Safari browser.", "Click_share": "Click \"share\"", "Tap_on_the_Share_i": "Tap on the \"Share\" icon in the menu at the bottom of the screen. A dialog box will open.\n", "Click_Add_to_Home_S": "Click \"Add to Home Screen\"", "Tap_on_the_item_Add": "Tap on the item \"Add to Home Screen\", and click\"Ready\"", "sys_update_info": "System update", "new_version_update_info": "There's a new version. Update now?", "Game_type_PVP": "PVP", "Game_type_arcade": "Arcade", "Game_type_casino": "Casino", "Game_type_fish": "Fishing", "Game_type_hot": "Hot", "Game_type_For_u": "For_U", "Game_type_poker": "Poker", "Game_type_slot": "Slot", "Game_type_bingo": "<PERSON><PERSON>", "Game_type_history": "History", "Game_type_new": "New", "Home_all": "ALL", "Banner_fund": "Click Here! </br>\nGet relief funds.", "Fund_claim_time": "<PERSON><PERSON><PERSON> times left:", "Fund_claim_Req": "Claim Requirement ：Balance < 1", "Fund_deposite_info": "₱{fund_deposite_nums} deposit adds 1 claim times.", "Fund_deposit_amount": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "claim_times": "claim times", "Fund_once_per_phone": "Claim once daily per phone", "Fund_no_phone": "You can only receive relief funds after binding your mobile phone", "Fund_no_event": "The relief fund event has already ended. Join us next time.", "Fund_no_claim_times": "Claim times used up. Click 'deposit' for more", "Fund_claim_aft_game": "Claim after game.", "Fund_still_ingame": "Still in-game.", "firstpay_intro": "The amount lost within 24 hours after this deposit will be returned!", "firstpay_details_1": "Deposited:", "firstpay_details_2": "Cumulative net loss:", "firstpay_details_3": "Return expected:", "firstpay_rules": "Event Rules", "firstpay_rules_1": "Choose any amount to deposit on the event page. Your cumulative net losses in bets within 24 hours after the deposit will be {number} returned after 24 hours.", "firstpay_rules_2": "The maximum return amount will not exceed the deposit made on this event.", "firstpay_rules_3": "Users can only select one of the specified deposit amounts from the event page. Any further deposits through the store will not count toward the event return limit.", "firstpay_rules_4": "Within 24 hours of the deposit, users can click the event icon or banner on the screen at any time to view their cumulative net loss amount.", "firstpay_rules_5": "The return amount will be available on the event page 24 hours after the deposit, and users can click to claim it at any time.", "firstpay_return": "{number} Cumulative net losses returned", "${receiver.ladderloserebate}": "You participated in the event \"Never Lose Money\" and received the reward {gold}", "Wallet_deposit_method": "Deposit Method", "Wallet_Free_Rewards": "Free Rewards", "Wallet_Withdraw_Amount": "Withdraw Amount", "scrolling_text_default": "Welcome to BetFugu. Wish you good luck.", "scrolling_text_sys_update": "The system will be updated from {time1} to {time2}", "banner_bind_phone": "bind your mobile number to save your a", "banner_earn_cash": "INVITE PLAYERS<br>\nEARN 50%<br>\nFROM REVENUES", "banner_relief_funds": "CLICK HERE!<br>\nGet relief funds", "event_bind_phone": "bind your mobile number to receive cash", "event_title_bindphone": "Bind mobile phone", "event_earn_cash": "INVITE PLAYERS<br>\nEARN 50%<br>\nFROM REVENUES", "menu_game_record": "Games Records", "menu_bind_phone": "Bind Phone", "event_end_time": "Ends on {last_time}", "event_relief_funds": "CLICK HERE!<br>\nGet relief funds", "event_title_relieffunds": "Relief Funds", "event_name_earncash": "<PERSON><PERSON><PERSON>", "wkqrpay": "&nbsp", "payermaxpay": "Goldpay", "wakapay": "Redpay", "letspay": "Redpay", "quiipay": "Greenpay", "ilivepay": "Bluepay", "halopay": "Orangepay", "xwinpay": "Greenpay", "PIXwakapay": "Greenpay", "wakapaypapara": "Redpay", "pageasy": "Bluepay", "antipay": "Purplepay", "ngnwakapay": "Bluepay", "capaywei": "Greenpay", "login_banner": "", "vip_banner": "", "invite_wheel": "", "card_upgrade_goal": "Accumulate Bet to Upgrade to VIP{level}", "card_restore_goal": "Monthly deposit to restore to VIP{maxlevel}", "vip_unlock": "Deposit To Unlock VIP", "vip_unlock_button": "Deposit {number} to Unlock", "vip_reward1": "Upgrade Cash", "vip_reward2": "Monthly Cash", "vip_reward3": "Bet Rebate", "vip_reward4": "Deposit Bonus", "vip_reward5": "Mystery Gift", "vip_reward6": "Daily Withdrawal", "vip_reward7": "Withdraw Limit", "info_upgrade_title1": "Upgrade to V{level}", "info_upgrade_goal1": "ACCUMULATE BET", "info_upgrade_title2": "Restore to V{maxlevel}", "info_upgrade_goal2": "MONTHLY DEPOSIT", "info_upgrade_title3": "Maintain V{level}", "info_upgrade_goal3": "MONTHLY BET", "info_reward": "REWARDS", "form_vipreward_title": "VIP Exclusive Perks & Rewards", "form_vipreward_1": "VIP LEVEL", "form_vipreward_2": "Level Up<br>\nReward", "form_vipreward_3": "Level Maint<br>\n<PERSON><PERSON>", "form_vipreward_4": "Bet<br>\nRebate", "form_vipreward_5": "Deposit<br>\nExtra Bonus", "form_vipreward_6": "Birthday<br>\nBonus", "form_vipreward_7": "Withdraw<br>\n<PERSON>it", "form_vipreward_8": "Daily<br>\n<PERSON><PERSON><PERSON>", "form_vipreward_9": "Free<br>\n<PERSON><PERSON><PERSON>", "form_vipreward_birth": "CS", "form_viplevel_title": " VIP TIER LEVEL", "form_viplevel_1": "Requirement<br>\nto Level-Up", "form_viplevel_2": "Requirement to<br>\nRetain Level", "vip_rule_title": "Become a VIP and Receive ₱77,777 Monthly", "vip_rule_details": "<span class=\"text-white\">Promotion Rules</span><br>After becoming a BetFugu VIP, you will unlock additional user benefits, regularly receive substantial bonuses, and enjoy fee coverage by the platform, further enhancing your gaming profits.<br><span class=\"text-white\">VIP Level Up Gift:</span> Users will receive an instant cash reward upon upgrading to the corresponding VIP level. This reward can be withdrawn after meeting a 5x Turnover requirement.<br><span class=\"text-white\">Monthly Salary:</span> VIP users can earn up to <span class=\"text-green\">₱77,777</span> per month, withdrawable after meeting a 5x Turnover requirement.<br>Weekly Allowance: Claim free betting credits every week! The higher your VIP level, the more free game entries you can receive up to ₱125 per week.<br><span class=\"text-white\">Daily Bonus Conversion: </span> Get daily cash based on your total bets. The higher your VIP level, the higher your bonus-to-cash conversion rate.<br><span class=\"text-white\">Daily Withdrawal Limit:</span> As the VIP level increases, the maximum daily withdrawal limit also increases.<br><span class=\"text-white\">Free Withdrawal:</span> When users make a withdrawal, banks charge a fee. The platform will cover part of this fee for VIP users.", "popup_upgrade_title": "Congratulations on leveling up to", "popup_upgrade_info": "Upgrade Reward(one-time)", "popup_maint_title": "Congratulations on maintaining", "popup_maint_info": "Maintain Reward(monthly)", "popup_downgrade_title1": "You have been downgraded to VIP {level}..", "popup_downgrade_title2": "Deposit a total of <span class=\"special\"> {value} </span>this month to<br>\ninstantly<span class=\"special\">  restore</span> your", "popup_downgrade_button": "Restore Now", "popup_restore_title": "Congratulations on restoring", "popup_bonus_title": "Rebates Every Day！", "popup_bonus_info": "PLEASE CLAIM YESTERDAY'S<br>\nBETTING RETURN", "bonus_explanation": "Bonus will be credited to your Vault at a rate of <span>{betrebate}</span> of your turnover amount. <br><br>You may withdraw the funds from your Vault to your cash balance and use them for betting.<br><br>Join the <span>Recharge and Get Extra</span> event and other promotions to earn more Bonus.!", "bonus_explanation_novip": "Top up any amount to unlock VIP. VIP users can convert their bonus amount into Cash balance.", "bonus_explanation_tip": "Up to Total Bet Bonus to Cash", "popup_vipreward_upgrade": "Upgrade Reward: {upreward}", "popup_vipreward_maintain": "Maintain Reward: {maintreward}", "popup_vipreward_birthday": "Birthday Bonus: ✔", "popup_vipreward_bet": "Bet Rebate: {betrebate}", "popup_vipreward_deposit3": "Deposit Bonus: {deposit} ", "popup_vipreward_withdraw1": "Withdraw Limit: {limit}", "popup_vipreward_withdraw2": "Daily Withdrawal: {times}", "message_claim_success": "<PERSON><PERSON> claimed successfully!", "message_claim_fail": "<PERSON><PERSON><PERSON> failed, please try again later!", "name": "Name", "withdraw_bind_name_info": "Please enter receiver's name", "withdraw_bind_name_error": "Please enter the correct form of the name", "withdraw_bind_account_info1": "Please enter your account (10 or 24 digits)", "withdraw_bind_account_error": "Please enter the correct account number", "withdraw_bind_way": "Withdrawal Way", "withdraw_bind_cpf": "CPF", "email": "EMAIL", "withdraw_bind_phone_info": "Please enter your phone number", "withdraw_bind_cpf_info": "Please enter your account (11 digits)", "withdraw_bind_email_info": "Please enter your receiver's email", "withdraw_bind_phone_error": "Please enter the correct phone number ", "withdraw_bind_cpf_error": "Please enter the correct account ", "withdraw_bind_email_error": "Please enter the correct receiver's email", "withdraw_bind_account_info2": "Please enter your account (10 digits)", "withdraw_limitation_info": "Daily Limitation", "withdraw_unlock_notif": "upgrade your vip level to unlock", "welcome_title": "Welcome To BetFugu!  <br>\n  Here is a special welcome gift.", "welcome_info": "1.Winnings can be withdrawn at any time<br>\n2.One player already got over <span class=\"text-orange\"> {number} </span> wins with free bet", "welcome_button": "Bind Phone & Play", "banner_fristpay": "LOOK HERE! <br> Deposit 200 Get 100 extra", "event_title_firstpay": "First Purchase", "event_firstpay": "LOOK HERE! <br> Deposit 200 Get 100 extra", "Firstpay_info": "Deposit <span class=\"text-red\"> {number1}  </span> Get  <span class=\"text-red\"> {number2}  </span> extra", "event_end": "This event has finished. Thanks.", "wallet_deposit_tier_unavailable": "Deposit tier unavailable. <PERSON><PERSON> another.", "wallet_deposit_roll": "Deposit channel under maintenance. Please contact customer service to deposit.", "wallet_Withdraw_roll": "Withdrawal channel under maintenance. Wait patiently.", "home_more": "More", "partner_game_tech": "Partner", "support_cunrrent": "Supported Currencies", "digital_pay": "Digital Pay Method", "licensing": "Licensing", "BetFugu_info1": "BetFugu is a renowned online gambling company, recognized for its commitment to legality and user safety. Licensed and regulated by the Curaçao gaming authority, the company offers a reliable and fair gaming experience for its customers. Our platform is designed to provide quality entertainment while maintaining high standards of security and transparency, so that users can enjoy every bet with confidence and peace of mind.", "${suggestion}": "Suggestion received! Thanks so much for your support.", "No_premission": "No permission", "Parameter_error": "Parameter error", "Main_account_login_again": "Please set the main account and log in again", "Redemptions_max_no_redeemed": "The maximum number of redemptions has been exceeded and cannot be redeemed", "mime_type_wrong": "Mimetype is wrong", "new_version_download_info": "Please download the new version to enjoy the game better.", "update_notice": "Update Notice", "banner_fristpay_brl": "LOOK HERE! <br> Deposit 10 Get 10 extra", "event_firstpay_brl": "LOOK HERE! <br> Deposit 10 Get 10 extra", "banner_fristpay_ngn": "LOOK HERE! <br> Deposit 1k Get 1k extra", "event_firstpay_ngn": "LOOK HERE! <br> Deposit 1k Get 1k extra", "banner_fristpay_usd": "LOOK HERE! <br> Deposit 9.99 Get 10 extra", "event_firstpay_usd": "LOOK HERE! <br> Deposit 9.99 Get 10 extra", "banner_fristpay_try": "LOOK HERE! <br> Deposit 200 Get 100 extra", "event_firstpay_try": "LOOK HERE! <br> Deposit 200 Get 100 extra", "letspay2": "Goldpay", "quiiqrpay": "&nbsp", "mayapay": "Greenpay", "brlilivepay": "Goldpay", "haipay": "Purplepay", "haipay2": "Purplepay", "capay": "Deepblue", "mmpay": "Olivepay", "mmpaymaya": "Limepay", "mmpaymya": "Indigopay", "Game_type_live": "Live", "Game_type_crash": "Crash", "Game_type_sport": "Sports", "Game_type_scratch": "<PERSON><PERSON><PERSON>", "Game_type_Bingo": "<PERSON><PERSON>", "Auto_reply": "Auto Reply", "Auto_reply_title": "Auto-Reply Message ", "Auto_message": "Message：", "Auto_reply_info": "Please enter your automatic reply message.(max 150 characters)\n", "max_characters": "<span class=\"text-orange\">{number_1}</span> characters", "Auto_reply_maxinput": "The maximum input length is 150 characters.", "Auto_set_success": "Auto - reply message is set successfully.\n", "Auto_set_fails": "Auto - replay message setting fails. \n", "${wallet_reseller_recall}": "Hi, I'd like to inquire about how to buy chips. Are you available now? ", "${wallet_reseller_hi}": "Hello, I'm an officially authorized reseller. What can I do for you?\n", "wallet_reseller_withdraw1": "Today's remaining transfer times: <span class=\"text-orange\"> {number1} </span>", "wallet_reseller_withdraw2": "Your withdrawal balance: <span class=\"text-orange\">{current_type} {number2} </span>", "wallet_reseller_withdraw3": "Transfer amount", "wallet_reseller_deposit1": "Your balance: <span class=\"text-orange\">{current_type} {number1} </span>", "wallet_reseller_deposit2": "Transfer amount:", "wallet_reseller_deposit3": "Please enter the transfer amount. ", "wallet_reseller_deposit4": "Minimum <span class=\"text-orange\"> {current_type} {number2}</span>", "wallet_reseller_deposit_fail1": "Transfer transaction failed. Your total transfer limit cannot exceed {number} times the deposit amount.", "wallet_reseller_deposit_fail2": "Incorrect input number or format.", "${message_transfer_touser_1}": "Transfer  <span class=\"text-yellow\">{current_type} {number2}</span> to <span class=\"text-yellow\">{Username}</span>", "${message_transfer_touser_2}": "You transfer {currency_type} {number2} to {Username}", "${message_transfer_touser_3}": "You've received a transfer of {number2} {currency_type} from {Username}.", "wallet_reseller_info": " Transfer <span class=\"text-orange\">{current_type} {current_number}  </span>to <span class=\"text-orange\">{username}</span>?", "error_info": "An error occurred. Please try again later.", "reels_unlock_one": "Unlock for <span class=\"text-orange\">{current_type} {current_number}  </span>. Confirm?", "reels_unlock_all": "<strong>Unlock Options:</strong></br>\nCurrent Ep.: <span class=\"text-orange\">{current_type} {current_number1}  </span></br>\nAll Upcoming Eps.:<span class=\"text-orange\">{current_type} {current_number2} </span>", "reels_balance_low": "The balance is low. Please deposit.", "reels_current": "Current", "reels_all": "ALL", "${reels_transaction_info}": "You spent {currency_type}  {number1} to unlock the <span class=\"underlined-red\">{reels_name} {EP1}. >> </span>", "earncash_join_group": "Join Group", "withdraw_bind_account_info3": "Please enter your account.", "reels_video_name": "{video_name}-EP.{number1}｜UP to {number2} EPS.", "reels_next_ep": "NEXT EP", "toppay": "Orangepay", "safepay": "Greenpay", "ngn24hrpay": "Orangepay", "reseller": "Manual Recharge", "Game_type_table": "Table", "Game_type_mines": " Mines", "wallet_withdraw_choose_channel": "<PERSON><PERSON><PERSON>", "uupay": "Orangepay", "uupay_10044": "Greenpay", "uupay_10041": "Goldpay", "uupay_10045": "Deepblue", "dalipay": "Redpay", "ilivepay2": "Orangepay", "halopay_QRCODE": "&nbsp", "ilivemaya": "Redpay", "safepayphp": "Violetpay", "sign_up_gift_1": "Sign up to get ", "sign_up_gift_2": "<span>{current_type}</span>{number} GIFTS", "welcome_gifts_1": "Welcome gifts", "welcome_gifts_2": "{game_type} <span class=\"text-yellow\">{current_type} {number}</span>", "welcome_gifts_3": "Welcome Bonuses", "congratulations": "Congratulations", "gift_bonus_1": "Bonus  {current_type} {number}", "unlock_vip": "Unlock at VIP 1 <span class=\"text-yellow\">(Deposit {number} -> Vip1)</span>", "bonus_vip_tocash": "Upto {number} Total Bet Bonus to Cash", "bonus_tovip_info": "Deposit <span class=\"text-red\">{number1} </span> to be VIP1 & unlock <span class=\"text-red\">{current_type} {number2} </span> bonus.", "gift_center": "Gift Center", "mines_freebet": "Mines Free bet", "mines_freebet_type": "Mines {current_type} {number} Free Bet", "super_scratch_free": "Super Scratch {number}", "super_scratch_type": "Super Scratch  {current_type} {number} Free", "mines_freebet_info": "Use this item to get <span class=\"text-green\">{current_type} {number}</span> free bets on Mines. Your winnings will be credited to your bonus account.", "super_scratch_free_info": "Use this item to get <span class=\"text-green\">{current_type} {number}</span> free bets on Scratch. Your winnings will be credited to your bonus account.", "giftcenter_nodata": " Oops! There is no data yet!", "bind_phone_security_info": "For account security, please bind your mobile number.", "gift_center_no_free_bet": "Not enough Free bet", "gift_center_no_this_gift": "The gift does not exist", "online_signin_banner": "", "cum_deposit_banner": "", "online_signin_title": "7 DAY<br>GIFTS", "claim_tmr": "Claim Tomorrow", "dps_to_claim": "Deposit {price} to <PERSON><PERSON><PERSON>", "firstdps_button": "Deposit {price} Now", "firstdps_banner": "", "firstdps_title": "FIRST<br>DEPOSIT", "bonus_to_cash_info": "Unlock VIP to Convert Your <span class=\"text-Red\"> Bonus</span> to <span class=\"text-Red\">Cash</span>.", "unlock_vip_1": "Unlock vip Now", "lastest_bet_bigwins": "Lastest bet & Big wins ", "lastest_bet": "Lastest bet ", "high_roller": "High Roller ", "higmutiplier": "High Multiplier", "profit": "profit", "Multiplier": "Multiplier", "spin_button": "Spin", "promotions_info": "Promotions", "Fisrt_deposit": "<PERSON><PERSON>rt Deposit", "Regular_deposit": "Regular Deposit", "Fisrt_deposit_upto": "First Deposit UP to <span class=\"text-green\">+ {current_type} {current_number}</span>", "Fisrt_deposit_info": "By choosing the First Deposit, <span class=\"text-Red\">{number}</span>.times your deposit amount will be added to your bet amount requirement. You must complete it before withdrawing.", "Deposit_button": "Deposit {current_type} {current_number} ", "wallet_total_amount": "Total Amount", "wallet_detail_info1": "Cash <span class=\"text-green\">{current_type} {current_number1} </span> +Bonus <span class=\"text-green\">{current_type} {current_number2} ", "wallet_detail_info2": "Cash <span class=\"text-green\">{current_type} {current_number1} </span> ", "wallet_bet_amount_left": "Bet amount Requirement", "wallet_commission": "Commission", "wallet_withdraw_info1": "You need to bet {number} more to withdraw.", "wallet_withdraw_info2": "Daily limit reached. Try again tomorrow or upgrade to VIP for higher limits.", "withdraw_button": "Withdraw {current_type} {current_number} ", "wallet_bet_withdrawal_limit": " Upgrade VIP level to increase daily withdrawal limitation.", "wallet_bet_amount_left_info_PHP": "Some of the Free Cash that players earn in <span class=\"text-green\">BetFugu</span> is subject to different betting requirements. Before making a withdrawal, you must complete your <span class=\"text-green\">betting requirements</span>.<br><br>The required betting amount is calculated based on the game type as follows:<br><ul><li>Perya = MIN( Bet,  ABS( Payoff - Bet ) )</li><li>Poker = MIN( Bet, ABS( Payoff - Bet ) )</li><li>Casino = MIN( Bet, ABS( Payoff - Bet ) )</li><li>Slot = Bet</li><li>Bingo = Bet</li><li>Arcade = Bet</li></ul>Note: <span class=\"text-green\">\"Bet\"</span> refers to the amount you wager, and <span class=\"text-green\">\"Payoff\"</span> refers to the amount you win from a game round.", "wallet_bet_amount_left_info_USD": "Some of the Free Cash that players earn in <span class=\"text-green\">BetFugu</span> is subject to different betting requirements. Before making a withdrawal, you must complete your <span class=\"text-green\">betting requirements</span>.<br><strong>How to Calculate Your Betting Amount:</strong><br><li> Calculated based on the actual amount bet.<br><li> For specific games, the calculation may vary depending on game rules (e.g., based on scratch ticket value, base points, or total win/loss).", "wallet_bet_amount_left_info_BRL": "Some of the Free Cash that players earn in <span class=\"text-green\">BetFugu</span> is subject to different betting requirements. Before making a withdrawal, you must complete your <span class=\"text-green\">betting requirements</span>.<br><strong>How to Calculate Your Betting Amount:</strong><br><li> Calculated based on the actual amount bet.<br><li> For specific games, the calculation may vary depending on game rules (e.g., based on scratch ticket value, base points, or total win/loss).", "wallet_bet_amount_left_info_TRY": "Some of the Free Cash that players earn in <span class=\"text-green\">BetFugu</span> is subject to different betting requirements. Before making a withdrawal, you must complete your <span class=\"text-green\">betting requirements</span>.<br><strong>How to Calculate Your Betting Amount:</strong><br><li> Calculated based on the actual amount bet.<br><li> For specific games, the calculation may vary depending on game rules (e.g., based on scratch ticket value, base points, or total win/loss).", "wallet_bet_amount_left_info_NGN": "Some of the Free Cash that players earn in <span class=\"text-green\">BetFugu</span> is subject to different betting requirements. Before making a withdrawal, you must complete your <span class=\"text-green\">betting requirements</span>.<br><strong>How to Calculate Your Betting Amount:</strong><br><li> Calculated based on the actual amount bet.<br><li> For specific games, the calculation may vary depending on game rules (e.g., based on scratch ticket value, base points, or total win/loss).", "claim_cash_times_info": "Requires {number}x bet amount to withdraw", "7day_cash_info": "Cash{current_type} {current_number} requires {time}× bet amounts to withdraw.", "7day_cash_info1": "Other cash requires {time}× bet amounts to withdraw.", "Fisrt_deposit_info1": "up to <span>+ {current_type}{current_number}</span> Cash Back", "Vault": "<PERSON><PERSON>", "Bonus_Deposit_info": "By choosing this deposit bonus, {times} times your deposit amount will be added to your Turnover requirement. You need to finish yours Turnover requirement before withdrawing.", "Confirmation": "Confirmation", "wallet_deposit_confirm": "Success! Please proceed to the deposit page.", "vip_reward_daily_title": "Bonus to Cash", "vip_reward_daily_desc": "Daily Bet to Convert Bonus", "vip_reward_levelup_title": "Level Up Gift", "vip_reward_levelup_desc": "Upgrade to VIP{value}", "vip_reward_month_title": "Monthly Salary", "vip_reward_month_desc": "Earn Cash Monthly", "vip_reward_week_title": "Weekly Allowance", "vip_reward_week_desc": "Free Bet Every Week", "vip_config_levelup_title": "VIP Level Up Gift", "vip_config_levelup_desc": "Enhance your VIP level and receive <span class=\"text-green\">{value}</span> cash reward immediately.", "vip_config_daily_title": "Daily Bonus Coversion", "vip_config_daily_desc": "Receive cash converted from bonus at <span class=\"text-green\">{value}</span> of your daily bet amount.", "vip_config_monthly_title": "Monthly Salary", "vip_config_monthly_desc": "Earn <span class=\"text-green\">{value}</span> monthly —— delivered to you on the 1st of every month.", "vip_config_weekly_title": "Weekly Allowance", "vip_config_weekly_desc": "Claim a weekly <span class=\"text-green\">{value}</span> allowance to enjoy free betting games!", "vip_config_nofee_title": "No Fee <PERSON>", "vip_config_nofee_desc": "Enjoy <span class=\"text-green\">{value} times</span> NO FEE withdrawal every day!", "vip_config_withdrwalimit_title": "Daily Withdraw Limit", "vip_config_withdrwalimit_desc": "Maximum daily withdrawal: <span class=\"text-green\">{value}</span><br>Enhance your VIP level to reach higher!", "vip_config_cs_title": "VIP Service Manager", "vip_config_cs_desc": "Your personal CS, ready to respond to your needs anytime!", "vip_config_birthday_title": "Birthday Treat", "vip_config_birthday_desc": "Celebrate your special day with exclusive birthday rewards!", "invite_wheel_icon_title": "Invite<br>Wheel", "invite_wheel_title": "Invite Wheel", "invite_state_title": "My amount", "invite_wheel_share_btn_txt": "Invite friends to get money", "invite_wheel_reward_desc": "Only <span class=\"text-yellow\">{remain}</span> left to get prize <span class=\"text-yellow\">{total}</span>", "invite_wheel_help_desc": "<li>Invite a friend to earn one spin of the wheel.<br><li>Any malicious attempts to gain extra spins for rewards will be considered cheating, and all eligibility for this round will be revoked.", "invite_wheel_reject": "Thank you for your participation.<br>Your withdrawal request has been reviewed and does not meet the current campaign criteria.<br>We encourage you to invite new genuine users.<br>For further details, please contact customer support.", "invite_wheel_reject_btn_txt": "Continue opening boxes! 🎁", "invite_wheel_cashout_share_btn_txt": "Invite friends to help", "invite_wheel_get_btn_txt": "Get it now", "under_review": "Under review, please wait patiently", "invite_wheel_award_won": "You Won", "invite_wheel_withdrawal_history": "Withdrawal history", "common_invite_share_txt_1": " \n🔥 The Invite Wheel is back — and it’s wilder than ever! \n🎯 Every spin is a 100% guaranteed win, up to ₱100! \n💥 No luck? No problem — the odds are on YOUR side now! \n🎁 Best part? Your first spin is a guaranteed BIG prize!⚡ Claim your prize instantly and start spinning now! \n👉 [Spin Now & Win Big]", "common_invite_share_txt_2": " \n🎉 Just tried the new Invite Wheel — and it’s absolutely wild! \n🎯 Every spin is a 100% guaranteed win, up to ₱100! \n💰 Higher chances, bigger rewards — totally worth it. \n🎁 And here’s the best part: your first spin is a guaranteed BIG prize!⚡ I’ve already claimed my prize and started playing — come join the fun! \n👉 [Grab your prize now & start spinning]", "common_invite_share_txt_3": " \n🔥 The new Invite Wheel is next-level fun! \n🎯 Every spin is a 100% guaranteed win, up to ₱100! \n💰 I’ve already won — and you can too! \n🎁 Sign up now and your first spin is a guaranteed BIG prize! No codes, no invites, no wait.🚀 Your prize is ready — claim it now and start playing! \n👉 [Claim Prize & Play Instantly]", "common_invite_share_txt1": "Share referral links", "Invite_Friends": "Invite Friends", "jackpot": {"title": "Jackpot of The Day", "daily_jackpot": "Daily Jackpot", "prize_pool": "Jackpot prize pool", "more": "More", "status": {"active": "Active"}, "navigation": {"history": "History", "my_rewards": "My Rewards"}, "table": {"rank": "#", "player": "Player", "turnover": "Turnover", "prize": "Prize", "status": "Status", "time": "Time"}, "time": {"remaining": "Time Remaining", "hour": "Hour", "minute": "Minute", "second": "Second"}, "champion": {"last": "Last Champion", "winner": "Winner"}, "winner": {"you_win": "You Win", "you_get": "YOU GET", "claim": "<PERSON><PERSON><PERSON>", "claiming": "Claiming..."}, "vip": {"to_share_jackpot": "to share jackpot", "go_bet": "GO BET", "my_rank": "My Rank", "turnover": "Turnover", "prize": "Prize", "bet": "Bet", "and_get": "and get", "congrats_rank_one": "Congrats! You're ranked No.1 right now!"}, "ranking": {"top_players": "Top Players", "leaderboard": "Leaderboard", "view_all": "View All"}, "rules": {"title": "Rules", "subtitle": "Jackpot of The Day Rules", "prize_calculation": "Prize Calculation Formula", "place_suffix": "place", "of_jackpot_pool": "of the Jackpot prize pool", "good_luck": "⭐⭐ Good luck and have fun! ⭐⭐", "rule_1": "The Jackpot prize pool closely depends on the bankroll, the more players bet the bigger it grows. Current prize pool will be shown on the Jackpot page.", "rule_2": "200 most Turnover players carve up the prize pool.", "rule_3": "This Jackpot support Turnover in all games:", "rule_3_sub_1": "Perya = MIN( Bet , ABS( Payoff - Bet ) )", "rule_3_sub_2": "Poker = MIN( Bet , ABS( Payoff - Bet ) )", "rule_3_sub_3": "Casino = MIN( Bet , ABS( Payoff - Bet ) )", "rule_3_sub_4": "Slot = Bet", "rule_3_sub_5": "Bingo = Bet", "rule_3_sub_6": "Arcade = Bet", "rule_4": "Prizes will be sent on Notification Page as the day ends. Please check and collect.", "rule_5": "Prizes requires 1x Turnover amount to withdraw.", "rule_6": "Minimum Turnover amount to join the Jackpot is 2000.", "rule_7": "BetFugu reserves the right to exclude players who have violated our rules at any stage.", "rule_8": "BetFugu reserves the right to change any rules and conditions at its sole discretion and without prior notice."}, "messages": {"no_data": "No data available", "loading": "Loading...", "error": "Error loading data", "coming_soon": "Coming Soon"}, "buttons": {"claim": "<PERSON><PERSON><PERSON>", "view_details": "View Details", "close": "Close", "confirm": "Confirm"}, "status_text": {"received": "Received", "not_received": "Not Received"}, "empty": {"message": "Oops! There is no data yet!"}}, "wallet": {"Maintenance": "Under maintenance, please choose another method."}, "withdrawal": {"INR": {"withdraw_bind_ifsc_error": "Please enter the correct IFSC code."}}, "login": {"login_select_game_title": "Select your sign up gift"}, "search_game": {"placeholder_game_name": "Game Name", "search_tip_min_chars": "Search requires at least 3 characters", "clear_search": "Clear Search", "history": "History", "recommend_games": "Recommend games", "sort_by": "Sort by:", "providers": "Providers:", "select": "Select", "clear_all": "Clear All", "sort_default": "<PERSON><PERSON><PERSON>", "sort_a_to_z": "A-Z", "sort_z_to_a": "Z-A"}, "Search_games": "Search games"}