<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 16:54:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-08-05 10:23:15
 * @FilePath     : /src/components/searchGame/GameGrid.vue
 * @Description  : 游戏网格列表组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 16:54:00
-->

<template>
    <div class="games-grid">
        <!-- 游戏列表 -->
        <div v-for="game in props.games" :key="game.gametag || game.id" class="game-item" @click="handleGameSelect(game)">
            <img v-lazy="game.picture" :alt="game.name" />
            <!-- 在线人数 - 右上角，与首页样式一致 -->
            <div v-if="game.gameonline" class="item-online">{{ game.gameonline }}</div>
            <!-- 底部信息 - 与首页样式一致 -->
            <div class="item-bot">
                <div class="item-name">
                    <div>{{ game.name }}</div>
                </div>
                <div v-if="game.gameHot && game.gameHot > 0" class="item-hot">{{ game.gameHot }}</div>
            </div>
        </div>

        <!-- 空状态显示 -->
        <div v-if="!props.games || props.games.length === 0" class="empty-state">
            <img src="@/assets/img/search_game/empty.png" alt="No data" class="empty-image" />
            <div class="empty-text">Oops! No data yet!</div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useGame } from '@/hooks/useGame'
import { Log } from '@/api/log'
import { type Game } from './gameSearchUtils'

interface Props {
    games?: Game[]
}

const props = withDefaults(defineProps<Props>(), {
    games: () => [],
})

const { goGamePage } = useGame()

const handleGameSelect = (game: Game) => {
    // 记录点击事件日志
    Log({
        event: `search_game_click_${game.gametag}`,
    })

    goGamePage(game)
}
</script>

<style lang="scss" scoped>
// 完全复制 tabList.vue 的样式，保持一致性
.games-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 14px;
    padding: 0 15px;
    padding-bottom: 30px;
    font-family: MicrosoftYaHei;

    // 当没有游戏时，改为 flex 布局以居中显示空状态
    &:has(.empty-state) {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 400px; // 给空状态一些高度
    }

    .game-item {
        position: relative;
        width: 230px;
        height: 280px;
        border-radius: 20px;
        overflow: hidden;
        cursor: pointer;

        img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .item-online {
            display: flex;
            align-items: center;
            gap: 6px;
            position: absolute;
            right: 8px;
            top: 8px;
            min-width: 80px;
            height: 32px;
            line-height: 32px;
            padding: 0 12px;
            background-image: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7));
            background-blend-mode: normal, normal;
            border-radius: 16px;
            background-size: 100% 100%;
            font-size: 18px;
            font-weight: bold;
            color: #fff;

            &::before {
                display: inline-block;
                content: '';
                width: 18px;
                height: 20px;
                background: url('@/assets/img/home/<USER>') no-repeat;
                background-size: 100% 100%;
            }
        }

        .item-bot {
            display: flex;
            align-items: center;
            gap: 6px;
            position: absolute;
            bottom: 10px;
            width: 100%;
            padding: 0 20px;
            font-size: 18px;
            line-height: 30px;
            color: #ffffff;

            .item-name {
                flex: 1;
                overflow: hidden;

                div {
                    white-space: nowrap; /* 保持文本在一行显示 */
                    overflow: hidden; /* 隐藏超出容器部分的文本 */
                    text-overflow: ellipsis;
                }
            }

            .item-hot {
                display: flex;
                align-items: center;
                gap: 6px;
                position: relative;
                &::before {
                    display: inline-block;
                    content: '';
                    width: 20px;
                    height: 22px;
                    background: url('@/assets/img/new-home/hot.png') no-repeat;
                    background-size: 100% 100%;
                }
            }
        }
    }

    // 空状态样式
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        margin-top: 100px;

        .empty-image {
            width: 440px;
        }

        .empty-text {
            font-size: 24px;
            font-weight: 500;
            color: #adb7ba;
            font-family: MicrosoftYaHei;
        }
    }
}

// 滚动条样式
:deep(.games-grid::-webkit-scrollbar) {
    width: 4px;
}

:deep(.games-grid::-webkit-scrollbar-track) {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

:deep(.games-grid::-webkit-scrollbar-thumb) {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

:deep(.games-grid::-webkit-scrollbar-thumb:hover) {
    background: rgba(255, 255, 255, 0.5);
}
</style>
