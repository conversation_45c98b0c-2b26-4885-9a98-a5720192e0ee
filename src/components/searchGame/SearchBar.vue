<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 16:51:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-08-05 11:19:11
 * @FilePath     : /src/components/searchGame/SearchBar.vue
 * @Description  : 搜索栏组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 16:51:00
-->

<template>
    <div class="search-bar-container">
        <div class="search-bar" :class="{ focused: isInputFocused }">
            <!-- 搜索输入框 -->
            <div class="search-field-wrapper">
                <input
                    ref="inputRef"
                    v-model="searchKeyword"
                    type="text"
                    :placeholder="$t('search_game.placeholder_game_name')"
                    class="search-input"
                    @input="handleInputChange"
                    @focus="handleInputFocus"
                    @blur="handleInputBlur"
                    @keyup.enter="handleSearch"
                />
                <!-- 清空按钮 -->
                <div v-if="searchKeyword" class="clear-btn" @click.stop="clearInput">
                    <van-icon name="cross" class="clear-icon" />
                </div>
            </div>

            <!-- 搜索按钮 -->
            <div class="search-icon-wrapper" @click.stop="handleSearch">
                <van-icon name="search" class="search-icon" />
            </div>
        </div>

        <!-- 搜索历史和推荐 -->
        <SearchHistory
            ref="searchHistoryRef"
            v-show="showSearchHistory"
            :visible="showSearchHistory"
            :keyword="searchKeyword"
            @select-history="onSelectHistory"
            @select-game="onSelectGame"
            @close="onCloseSearchHistory"
        />
    </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount } from 'vue'
import SearchHistory from './SearchHistory.vue'

// 定义属性
interface Props {
    modelValue?: string
}

// 定义事件
interface Emits {
    (e: 'update:modelValue', value: string): void
    (e: 'search', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
})

const emit = defineEmits<Emits>()

const searchKeyword = ref(props.modelValue) // 输入框显示的值（实时更新）
const actualSearchKeyword = ref('') // 真正用于搜索的值（延迟更新）
const showSearchHistory = ref(false)
const searchHistoryRef = ref<InstanceType<typeof SearchHistory> | null>(null)
const isInputFocused = ref(false)
const inputRef = ref<HTMLInputElement | null>(null)
const searchTimer = ref<number | null>(null) // 防抖定时器

// 清除防抖定时器
const clearSearchTimer = () => {
    if (searchTimer.value) {
        clearTimeout(searchTimer.value)
        searchTimer.value = null
    }
}

// 执行真正的搜索（添加历史记录）
const performActualSearch = (keyword: string) => {
    actualSearchKeyword.value = keyword

    // 添加到搜索历史
    if (keyword.trim().length > 0) {
        if (searchHistoryRef.value) {
            searchHistoryRef.value.addSearchHistory(keyword)
        }
    }

    // 发送搜索事件给父组件
    emit('search', keyword)

    // 隐藏搜索历史面板
    showSearchHistory.value = false
}

// 防抖搜索（用户停止输入后触发）
const debouncedSearch = (keyword: string, delay: number = 500) => {
    // 清除之前的定时器
    clearSearchTimer()

    // 设置新的定时器
    searchTimer.value = setTimeout(() => {
        performActualSearch(keyword)
        searchTimer.value = null
    }, delay)
}

const clearInput = () => {
    searchKeyword.value = ''
    actualSearchKeyword.value = ''
    emit('update:modelValue', '')

    // 清除防抖定时器
    clearSearchTimer()

    // 清空后显示搜索历史面板
    showSearchHistory.value = true
}

// 立即搜索（回车键、搜索按钮点击）
const handleSearch = () => {
    // 清除防抖定时器（因为要立即搜索）
    clearSearchTimer()

    // 立即执行搜索
    performActualSearch(searchKeyword.value)
}

// 输入变化处理（防抖搜索）
const handleInputChange = (event: Event) => {
    const target = event.target as HTMLInputElement
    const value = target.value

    // 更新输入框显示值
    searchKeyword.value = value
    emit('update:modelValue', value)

    // 根据输入长度控制搜索历史面板显示和搜索行为
    if (value.length < 3) {
        showSearchHistory.value = true
        // 清除防抖定时器（输入太短不搜索）
        clearSearchTimer()
    } else {
        showSearchHistory.value = false
        // 启动防抖搜索（用户停止输入500ms后执行）
        debouncedSearch(value, 500)
    }
}

// 选择历史记录（立即搜索）
const onSelectHistory = (keyword: string) => {
    searchKeyword.value = keyword
    emit('update:modelValue', keyword)

    // 清除防抖定时器并立即搜索
    clearSearchTimer()
    performActualSearch(keyword)
}

// 选择游戏（立即搜索）
const onSelectGame = (game: any) => {
    searchKeyword.value = game.name
    emit('update:modelValue', game.name)

    // 清除防抖定时器并立即搜索
    clearSearchTimer()
    performActualSearch(game.name)
}

// 关闭搜索历史
const onCloseSearchHistory = () => {
    showSearchHistory.value = false
}

// 处理输入框聚焦
const handleInputFocus = () => {
    isInputFocused.value = true
    // 聚焦时显示搜索历史
    showSearchHistory.value = true
}

// 处理输入框失焦
const handleInputBlur = () => {
    isInputFocused.value = false
}

// 点击外部关闭搜索历史
const handleClickOutside = (event: MouseEvent) => {
    const searchBarContainer = document.querySelector('.search-bar-container')
    if (searchBarContainer && !searchBarContainer.contains(event.target as Node)) {
        showSearchHistory.value = false
    }
}

// 生命周期
onMounted(() => {
    document.addEventListener('click', handleClickOutside)
})

onBeforeUnmount(() => {
    document.removeEventListener('click', handleClickOutside)
    clearSearchTimer() // 清理防抖定时器
})

// 监听外部值变化
watch(
    () => props.modelValue,
    (newValue) => {
        searchKeyword.value = newValue
    }
)

// 监听searchKeyword变化，控制搜索历史面板显示
watch(
    () => searchKeyword.value,
    (newValue) => {
        // 当输入长度>=3时，隐藏搜索历史面板
        if (newValue && newValue.length >= 3) {
            showSearchHistory.value = false
        }
    }
)
</script>

<style lang="scss" scoped>
$text-primary: #ffffff;
$text-placeholder: rgba(255, 255, 255, 0.6);
$input-bg: rgba(255, 255, 255, 0.08);
$icon-color: rgba(255, 255, 255, 0.8);

.search-bar-container {
    position: relative;
    z-index: 100;
}

.search-bar {
    margin: 20px 15px 0;
    padding: 12px 16px;
    flex-shrink: 0;
    height: 80px;
    display: flex;
    align-items: center;
    border-radius: 8px;
    background: $input-bg;
    border: 1px solid #ffffff0d;
    transition: border-color 0.2s ease;

    &.focused {
        border-color: #2cee88 !important;
    }

    // 搜索输入框样式
    .search-field-wrapper {
        flex: 1;
        height: 100%;
        margin: 0 10px;
        display: flex;
        align-items: center;
        position: relative;

        .search-input {
            width: 100%;
            height: 70%;
            background: transparent;
            border: none;
            outline: none;
            color: $text-primary;
            font-size: 26px;
            padding: 0;
            padding-right: 40px; // 为清空按钮留出空间

            &::placeholder {
                color: $text-placeholder;
            }
        }

        .clear-btn {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            background: #ffffff1a;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s;

            &:hover {
                background: rgba(255, 255, 255, 0.3);
            }

            .clear-icon {
                color: $text-primary;
                font-size: 18px;
            }
        }
    }

    // 搜索图标样式
    .search-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: 0 12px;
        cursor: pointer;

        .search-icon {
            color: $icon-color;
            font-size: 36px;
        }
    }
}
</style>
