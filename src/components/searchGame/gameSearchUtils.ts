/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-08-01 19:22:39
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-08-01 21:27:17
 * @FilePath     : /src/components/searchGame/gameSearchUtils.ts
 * @Description  : 游戏搜索相关的工具函数
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-08-01 19:22:39
 */
/**
 * 游戏搜索工具函数
 * @description 包含游戏数据处理、筛选、统计等功能
 * <AUTHOR> Assistant
 * @date 2025-08-01
 */

import { Gamefirm, Categorys } from '@/api/home/<USER>'

// 游戏接口定义（基于真实游戏数据结构）
export interface Game {
    id?: string | number
    name?: string
    gamefirm?: string // 对应供应商的 abbr 字段
    gametag?: string
    picture?: string // 游戏图片
    gameonline?: number // 在线人数
    gameHot?: number // 热度
    categoryType?: string
    categoryName?: string
    // 兼容旧的数据结构
    provider?: string
    image?: string
    tag?: string
    category?: string
}

/**
 * 构建包含所有分类游戏的统一列表
 * @description 将分散在各个分类下的游戏收集到一个列表中，并为每个游戏添加分类信息
 * @param store 基础数据 store
 * @returns 包含所有游戏的统一列表
 */
export function buildAllGamesList(store: any): Game[] {
    if (!store.gameList || !store.menuData) return []

    const games: Game[] = []

    // 遍历所有分类
    store.menuData.forEach((category: Categorys) => {
        const categoryGames = store.getGameList(category.type) || []

        // 为每个游戏添加分类信息
        categoryGames.forEach((game: any) => {
            games.push({
                ...game,
                categoryType: category.type, // 使用 type 作为分类标识
                categoryName: category.name,
            })
        })
    })

    return games
}

/**
 * 按分类筛选游戏
 * @param games 游戏列表
 * @param selectedCategory 选中的分类
 * @returns 筛选后的游戏列表
 */
export function filterGamesByCategory(games: Game[], selectedCategory: string): Game[] {
    if (selectedCategory === 'all') return games
    return games.filter((game) => game.categoryType === selectedCategory)
}

/**
 * 按搜索关键词筛选游戏
 * @param games 游戏列表
 * @param keyword 搜索关键词
 * @param store 基础数据 store（用于获取供应商名称）
 * @returns 筛选后的游戏列表
 */
export function filterGamesByKeyword(games: Game[], keyword: string, store: any): Game[] {
    if (!keyword) return games

    const lowerKeyword = keyword.toLowerCase()
    return games.filter((game) =>
        (game.name && game.name.toLowerCase().includes(lowerKeyword)) ||
        (game.gamefirm && store.getGameName(game.gamefirm).toLowerCase().includes(lowerKeyword)) ||
        (game.gametag && game.gametag.toLowerCase().includes(lowerKeyword))
    )
}

// 扩展的供应商接口，包含游戏数量
export interface ProviderWithStats extends Gamefirm {
    gameCount: number
}

/**
 * 按供应商筛选游戏
 * @param games 游戏列表
 * @param providerValue 供应商筛选值（'All' 或供应商缩写列表，如 'pragmatic,pgsoft'）
 * @returns 筛选后的游戏列表
 */
export function filterGamesByProvider(games: Game[], providerValue: string): Game[] {
    if (providerValue === 'All') return games

    // 将供应商缩写字符串分割为数组
    const selectedAbbrs = providerValue.split(',').map(abbr => abbr.trim().toLowerCase())

    return games.filter((game) => {
        // 直接比较 game.gamefirm 与选中的供应商缩写
        return game.gamefirm && selectedAbbrs.includes(game.gamefirm.toLowerCase())
    })
}

/**
 * 按排序方式排序游戏
 * @param games 游戏列表
 * @param sortBy 排序方式
 * @returns 排序后的游戏列表
 */
export function sortGames(games: Game[], sortBy: string): Game[] {
    if (sortBy === 'A-Z') {
        return games.slice().sort((a, b) => {
            const nameA = (a.name || '').toLowerCase()
            const nameB = (b.name || '').toLowerCase()
            return nameA.localeCompare(nameB)
        })
    } else if (sortBy === 'Z-A') {
        return games.slice().sort((a, b) => {
            const nameA = (a.name || '').toLowerCase()
            const nameB = (b.name || '').toLowerCase()
            return nameB.localeCompare(nameA)
        })
    }
    return games // Default 排序
}

/**
 * 计算单个供应商在已筛选游戏列表中的数量
 * @param providerAbbr 供应商缩写（abbr 字段）
 * @param filteredGameList 已筛选的游戏列表
 * @returns 游戏数量
 */
export function getProviderGameCount(providerAbbr: string, filteredGameList: Game[]): number {
    if (!providerAbbr || !filteredGameList || !Array.isArray(filteredGameList)) {
        return 0
    }

    return filteredGameList.filter(game =>
        game.gamefirm && game.gamefirm === providerAbbr
    ).length
}

/**
 * 为供应商列表添加基于已筛选游戏的数量统计
 * @param providers 供应商列表
 * @param filteredGameList 已筛选的游戏列表（来自 index.vue 的 filteredGames）
 * @returns 包含实时游戏数量的供应商列表
 */
export function addGameCountToProviders(
    providers: Gamefirm[],
    filteredGameList: Game[]
): ProviderWithStats[] {
    if (!providers || !Array.isArray(providers)) {
        return []
    }

    if (!filteredGameList || !Array.isArray(filteredGameList)) {
        return providers.map(provider => ({
            ...provider,
            gameCount: 0
        }))
    }

    return providers.map(provider => {
        const gameCount = getProviderGameCount(provider.abbr, filteredGameList)

        return {
            ...provider,
            gameCount
        }
    })
}