<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 15:56:33
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-08-05 10:55:08
 * @FilePath     : /src/components/searchGame/input.vue
 * @Description  : 游戏搜索框组件（仅展示样式，无输入功能）
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 15:56:33
-->

<template>
    <div class="search-game-input">
        <!-- 搜索框外观 -->
        <div class="search-bar" @click="handleClick">
            <!-- 搜索图标 -->
            <div class="search-icon-wrapper">
                <van-icon name="search" class="search-icon" />
            </div>

            <!-- 搜索文案 -->
            <div class="search-text">
                {{ $t('Search_games') }}
            </div>
        </div>

        <!-- 搜索弹窗 -->
        <van-popup v-model:show="isSearchVisible" position="right" :style="{ width: '100%', height: '100%' }" :close-on-click-overlay="false">
            <SearchGameIndex @close="closeSearchPopup" />
        </van-popup>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SearchGameIndex from './index.vue'

// 定义事件
interface Emits {
    (e: 'click'): void
}

const emit = defineEmits<Emits>()

// 搜索相关状态
const isSearchVisible = ref(false)

// 方法
const closeSearchPopup = () => {
    isSearchVisible.value = false
}

// 处理点击事件
const handleClick = () => {
    emit('click')
    // 显示搜索弹窗
    isSearchVisible.value = true
}
</script>

<style lang="scss" scoped>
$text-primary: #ffffff;
$text-placeholder: rgba(255, 255, 255, 0.6);
$input-bg: rgba(255, 255, 255, 0.08);
$icon-color: rgba(255, 255, 255, 0.8);

.search-game-input {
    width: 100%;
    padding: 0 15px;
}

.search-bar {
    padding: 12px 16px;
    height: 64px;
    display: flex;
    align-items: center;
    border-radius: 8px;
    background: $input-bg;
    border: 1px solid #ffffff0d;

    // 搜索图标样式
    .search-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;

        .search-icon {
            color: $icon-color;
            font-size: 32px;
        }
    }

    // 搜索文案样式
    .search-text {
        flex: 1;
        color: $text-placeholder;
        font-size: 24px;
        user-select: none;
    }
}
</style>
